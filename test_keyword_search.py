#!/usr/bin/env python3
"""
关键词搜索功能测试脚本

测试新实现的关键词搜索功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.setdefault("ENVIRONMENT", "development")

try:
    from controllers.douyin import douyin_controller
    from tasks.core.models import TaskConfig
    from tasks.core.logger import TaskLogger
    from tasks.monitors.keyword import KeywordMonitorTask
    print("✅ 所有模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    sys.exit(1)


async def test_douyin_search_videos():
    """测试抖音搜索视频功能"""
    print("=== 测试抖音搜索视频功能 ===")
    
    try:
        # 测试搜索视频
        result = await douyin_controller.search_videos(
            keyword="美食",
            count=5,
            offset=0
        )
        
        print(f"搜索结果状态码: {result.get('status_code')}")
        print(f"搜索结果数量: {len(result.get('data', []))}")
        
        # 显示前几个结果
        data = result.get('data', [])
        for i, item in enumerate(data[:3], 1):
            if item.get('type') == 1 and 'aweme_info' in item:
                aweme_info = item['aweme_info']
                print(f"  {i}. {aweme_info.get('desc', '')[:50]}...")
                print(f"     作者: {aweme_info.get('author', {}).get('nickname', '未知')}")
                print(f"     视频ID: {aweme_info.get('aweme_id')}")
        
        return True
        
    except Exception as e:
        print(f"搜索视频测试失败: {str(e)}")
        return False


async def test_keyword_monitor_search():
    """测试关键词监控任务的搜索功能"""
    print("\n=== 测试关键词监控任务搜索功能 ===")
    
    try:
        # 创建任务配置
        config = TaskConfig(
            task_type="keyword_monitor",
            batch_size=10,
            timeout=60,
            max_age_hours=1,
            keyword_video_limit=5,
            keyword_search_days=7,
        )
        
        # 创建任务日志
        logger = TaskLogger("test_keyword_search")
        
        # 创建关键词监控任务
        task = KeywordMonitorTask(config, logger)
        
        # 测试搜索功能
        videos = await task._search_keyword_videos("美食")
        
        print(f"关键词监控搜索结果数量: {len(videos)}")
        
        # 显示前几个结果
        for i, video in enumerate(videos[:3], 1):
            print(f"  {i}. {video.get('desc', '')[:50]}...")
            print(f"     作者: {video.get('author', {}).get('nickname', '未知')}")
            print(f"     视频ID: {video.get('aweme_id')}")
        
        return True
        
    except Exception as e:
        print(f"关键词监控搜索测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("开始测试关键词搜索功能...\n")
    
    # 测试抖音搜索视频功能
    search_success = await test_douyin_search_videos()
    
    # 测试关键词监控任务搜索功能
    monitor_success = await test_keyword_monitor_search()
    
    # 总结测试结果
    print("\n=== 测试结果总结 ===")
    print(f"抖音搜索视频功能: {'✅ 成功' if search_success else '❌ 失败'}")
    print(f"关键词监控搜索功能: {'✅ 成功' if monitor_success else '❌ 失败'}")
    
    if search_success and monitor_success:
        print("\n🎉 所有测试通过！关键词搜索功能实现成功！")
        return 0
    else:
        print("\n⚠️  部分测试失败，请检查实现。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
