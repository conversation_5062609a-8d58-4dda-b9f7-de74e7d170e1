"""
奇好助手相关模型
"""

from tortoise import fields, models

from models.base import SoftDeleteManager


class UserInboxSourceRelated(models.Model):
    """用户收件箱来源关联表"""

    # 主键UUID
    uuid = fields.CharField(max_length=32, pk=True, description="主键UUID")

    # 时间字段 - 使用自定义字段名以匹配SQL表结构
    create_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.DatetimeField(auto_now=True, description="更新时间")

    # 软删除字段
    is_deleted = fields.BooleanField(default=False, description="是否删除")

    # 业务字段
    user_uuid = fields.CharField(max_length=32, description="用户UUID")
    source_id = fields.CharField(max_length=32, description="来源ID")
    source_type = fields.CharField(max_length=10, default="video", description="来源类型(video/author)")
    deleted_at = fields.Char<PERSON><PERSON>(max_length=32, default="", null=True, description="删除时间")

    # 软删除管理器
    objects = None

    def __init_subclass__(cls, **kwargs):
        """子类初始化时设置软删除管理器"""
        super().__init_subclass__(**kwargs)
        if not hasattr(cls, "_meta") or not getattr(cls._meta, "abstract", False):
            cls.objects = SoftDeleteManager(cls)

    async def soft_delete(self):
        """软删除记录"""
        self.is_deleted = True
        await self.save(update_fields=["is_deleted", "update_time"])

    async def restore(self):
        """恢复软删除的记录"""
        self.is_deleted = False
        await self.save(update_fields=["is_deleted", "update_time"])

    class Meta:
        table = "user_inbox_source_related"
        indexes = [
            ("user_uuid",),  # idx_user_keyword_user
            ("source_id",),  # idx_user_source_id
            ("source_type",),  # idx_user_source_type
            ("user_uuid", "source_type", "source_id"),  # idx_user_source_type_id
        ]
        # 唯一约束：uk_user_source_type_id
        unique_together = [("user_uuid", "source_type", "source_id", "deleted_at")]

    def __str__(self):
        return f"UserInboxSourceRelated({self.uuid}, {self.user_uuid}, {self.source_type}:{self.source_id})"
