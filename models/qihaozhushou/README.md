# 奇好助手模型使用指南

本文档说明如何使用奇好助手相关的 Tortoise ORM 模型。

## 📁 模型文件结构

```
models/qihaozhushou/
├── __init__.py      # 模型导入文件
├── models.py        # 奇好助手模型定义
└── README.md        # 使用指南（本文件）
```

## 🗂️ 模型概览

### 用户收件箱来源关联模型

| 模型 | 描述 | 主要字段 |
|------|------|----------|
| `UserInboxSourceRelated` | 用户收件箱来源关联表 | uuid, user_uuid, source_id, source_type |

## 🚀 使用方法

### 1. 导入模型

```python
# 方式1: 从主模块导入(推荐)
from models import UserInboxSourceRelated

# 方式2: 从子模块导入
from models.qihaozhushou import UserInboxSourceRelated
```

### 2. 基本 CRUD 操作

#### 创建记录

```python
import uuid

# 生成UUID
record_uuid = str(uuid.uuid4()).replace('-', '')
user_uuid = str(uuid.uuid4()).replace('-', '')
source_id = str(uuid.uuid4()).replace('-', '')

# 创建视频来源关联
video_relation = await UserInboxSourceRelated.create(
    uuid=record_uuid,
    user_uuid=user_uuid,
    source_id=source_id,
    source_type="video"
)

# 创建作者来源关联
author_relation = await UserInboxSourceRelated.create(
    uuid=str(uuid.uuid4()).replace('-', ''),
    user_uuid=user_uuid,
    source_id="author_123",
    source_type="author"
)
```

#### 查询记录

```python
# 根据UUID查询
relation = await UserInboxSourceRelated.get(uuid=record_uuid)

# 根据用户UUID查询所有关联
user_relations = await UserInboxSourceRelated.filter(
    user_uuid=user_uuid,
    is_deleted=False
).all()

# 查询特定类型的关联
video_relations = await UserInboxSourceRelated.filter(
    user_uuid=user_uuid,
    source_type="video",
    is_deleted=False
).all()

# 查询特定来源的关联
source_relations = await UserInboxSourceRelated.filter(
    source_id=source_id,
    is_deleted=False
).all()
```

#### 更新记录

```python
# 更新记录
relation = await UserInboxSourceRelated.get(uuid=record_uuid)
relation.source_type = "author"
await relation.save()

# 批量更新
await UserInboxSourceRelated.filter(
    user_uuid=user_uuid,
    source_type="video"
).update(source_type="updated_video")
```

#### 删除记录

```python
# 软删除（推荐）
relation = await UserInboxSourceRelated.get(uuid=record_uuid)
await relation.soft_delete()

# 恢复软删除的记录
await relation.restore()

# 物理删除（谨慎使用）
await UserInboxSourceRelated.filter(uuid=record_uuid).delete()
```

### 3. 高级查询

```python
# 查询用户的所有视频来源
user_video_sources = await UserInboxSourceRelated.filter(
    user_uuid=user_uuid,
    source_type="video",
    is_deleted=False
).values_list("source_id", flat=True)

# 统计用户的来源数量
source_count = await UserInboxSourceRelated.filter(
    user_uuid=user_uuid,
    is_deleted=False
).count()

# 按来源类型分组统计
from tortoise.functions import Count
stats = await UserInboxSourceRelated.filter(
    user_uuid=user_uuid,
    is_deleted=False
).group_by("source_type").annotate(
    count=Count("uuid")
).values("source_type", "count")
```

## 🎯 注意事项

1. **主键字段**: 使用 `uuid` 作为主键，而不是自增ID
2. **唯一约束**: `(user_uuid, source_type, source_id, deleted_at)` 组合唯一
3. **软删除**: 模型支持软删除功能，建议使用软删除而不是物理删除
4. **时间字段**: 使用 `create_time` 和 `update_time` 而不是标准的 `created_at` 和 `updated_at`
5. **索引优化**: 已为常用查询字段创建索引，提高查询性能

## 📊 数据库表结构

```sql
CREATE TABLE user_inbox_source_related (
    uuid VARCHAR(32) NOT NULL PRIMARY KEY COMMENT '主键UUID',
    create_time DATETIME(6) NOT NULL COMMENT '创建时间',
    update_time DATETIME(6) NOT NULL COMMENT '更新时间',
    is_deleted BOOL NOT NULL COMMENT '是否删除' DEFAULT 0,
    user_uuid VARCHAR(32) NOT NULL COMMENT '用户UUID',
    source_id VARCHAR(32) NOT NULL COMMENT '来源ID',
    source_type VARCHAR(10) NOT NULL COMMENT '来源类型(video/author)' DEFAULT 'video',
    deleted_at VARCHAR(32) COMMENT '删除时间' DEFAULT '',
    UNIQUE KEY uk_user_source_type_id (user_uuid, source_type, source_id, deleted_at),
    KEY idx_user_keyword_user (user_uuid),
    KEY idx_user_source_id (source_id),
    KEY idx_user_source_type (source_type),
    KEY idx_user_source_type_id (user_uuid, source_type, source_id)
) CHARACTER SET utf8mb4 COMMENT='用户收件箱来源关联表';
```

这个模型提供了完整的用户收件箱来源关联数据存储解决方案。
