"""
关键词监控任务 - 监控TrendInsight关键词并搜索相关新视频
"""

import asyncio
from datetime import datetime, timedelta
from typing import AsyncIterator, Dict, List, Union

from controllers.douyin import DouyinController
from models.trendinsight import TrendInsightKeyword
from services import VideoSyncService, VideoRelationService, KeywordService, UserInboxService
from services.base import FilterCriteria
from tasks.core.base import BaseTask
from tasks.core.logger import TaskLogger
from tasks.core.models import TaskConfig, TaskResult
from tasks.monitors.base import create_monitor, profile_operation


class KeywordMonitorTask(BaseTask):
    """关键词监控任务 - 监控关键词并搜索相关新视频"""

    def __init__(self, config: TaskConfig, logger: TaskLogger):
        """
        初始化关键词监控任务

        Args:
            config: 任务配置
            logger: 日志记录器
        """
        super().__init__(config, logger)
        self.douyin_controller = DouyinController()
        self.monitor = create_monitor("keyword_monitor")
        self.api_calls = 0

        # 初始化服务层
        self.video_sync_service = VideoSyncService(logger.logger)
        self.video_relation_service = VideoRelationService(logger.logger)
        self.keyword_service = KeywordService(logger.logger)
        self.user_inbox_service = UserInboxService(logger.logger)

    async def validate_params(self) -> bool:
        """
        验证任务参数

        Returns:
            bool: 参数是否有效
        """
        try:
            # 验证过滤条件
            filters = self.config.filters or {}
            if "keywords" in filters:
                keywords = filters["keywords"]
                if not isinstance(keywords, list) or not all(isinstance(kw, str) for kw in keywords):
                    self.logger.log_error("keywords 过滤条件必须是字符串列表")
                    return False

            if "exclude_keywords" in filters:
                exclude_keywords = filters["exclude_keywords"]
                if not isinstance(exclude_keywords, list) or not all(isinstance(kw, str) for kw in exclude_keywords):
                    self.logger.log_error("exclude_keywords 过滤条件必须是字符串列表")
                    return False

            return True

        except Exception as e:
            self.logger.log_error(f"参数验证失败: {str(e)}")
            return False

    async def execute(self) -> TaskResult:
        """
        执行关键词监控任务

        Returns:
            TaskResult: 执行结果
        """
        start_time = datetime.now()
        processed_count = 0
        success_count = 0
        failed_count = 0
        errors = []

        try:
            self.logger.log_progress(0, message="开始查询过期关键词数据")

            # 流式查询过期关键词
            async for batch_keywords in self._query_stale_keywords_batch():
                if self.is_interrupted:
                    self.logger.log_warning("任务被中断，停止处理")
                    break

                # 批量处理关键词
                batch_results = await self._process_keyword_batch(batch_keywords)

                # 更新统计信息
                processed_count += len(batch_keywords)
                success_count += batch_results["success_count"]
                failed_count += batch_results["failed_count"]
                errors.extend(batch_results["errors"])

                # 记录进度（每20条记录）
                if processed_count % 20 == 0 or processed_count < 20:
                    self.logger.log_progress(
                        processed_count,
                        message=f"已处理 {processed_count} 个关键词，成功 {success_count}，失败 {failed_count}",
                    )

                    # 记录性能指标
                    self.monitor.log_performance_metrics(processed_count=processed_count, api_calls=self.api_calls)

                    # 检查内存使用
                    self.monitor.check_memory_threshold(threshold_mb=512.0)

            # 确定任务状态
            if processed_count == 0:
                status = "success"
                self.logger.log_progress(0, message="没有需要监控的过期关键词")
            elif failed_count == 0:
                status = "success"
            elif success_count > 0:
                status = "partial"
            else:
                status = "failed"

            # 生成性能报告
            performance_report = self.monitor.generate_performance_report(
                final_processed_count=processed_count, final_api_calls=self.api_calls
            )

            result = self._create_result(
                status=status,
                start_time=start_time,
                processed_count=processed_count,
                success_count=success_count,
                failed_count=failed_count,
                errors=errors,
            )

            # 添加性能报告到结果
            if hasattr(result, "performance"):
                result.performance = performance_report

            return result

        except Exception as e:
            error_msg = f"任务执行失败: {str(e)}"
            self.logger.log_error(error_msg)
            errors.append(error_msg)

            return self._create_result(
                status="failed",
                start_time=start_time,
                processed_count=processed_count,
                success_count=success_count,
                failed_count=failed_count,
                errors=errors,
            )

    async def _query_stale_keywords_batch(self) -> AsyncIterator[List[TrendInsightKeyword]]:
        """
        流式批量查询过期关键词

        Yields:
            List[TrendInsightKeyword]: 关键词批次
        """
        try:
            # 计算过期时间阈值
            cutoff_time = datetime.now() - timedelta(hours=self.config.max_age_hours)

            # 构建查询条件
            query = TrendInsightKeyword.filter(updated_at__lte=cutoff_time)

            # 应用过滤条件
            filters = self.config.filters or {}
            if "keywords" in filters:
                keywords = filters["keywords"]
                query = query.filter(keyword__in=keywords)

            if "exclude_keywords" in filters:
                exclude_keywords = filters["exclude_keywords"]
                query = query.exclude(keyword__in=exclude_keywords)

            # 分批查询
            offset = 0
            batch_size = self.config.batch_size

            while True:
                if self.is_interrupted:
                    break

                batch = await query.offset(offset).limit(batch_size).all()
                if not batch:
                    break

                yield batch
                offset += batch_size

                # 批次间短暂休息，避免数据库压力
                await asyncio.sleep(0.1)

        except Exception as e:
            self.logger.log_error(f"查询过期关键词失败: {str(e)}")
            raise

    async def _process_keyword_batch(self, keywords: List[TrendInsightKeyword]) -> Dict[str, Union[int, List[str]]]:
        """
        批量处理关键词监控

        Args:
            keywords: 关键词列表

        Returns:
            dict: 批次处理结果
        """
        success_count = 0
        failed_count = 0
        errors = []

        for keyword in keywords:
            if self.is_interrupted:
                break

            try:
                success = await self._monitor_keyword_videos(keyword)
                if success:
                    success_count += 1
                else:
                    failed_count += 1
                    errors.append(f"监控关键词 {keyword.keyword} 视频失败")

            except Exception as e:
                failed_count += 1
                error_msg = f"处理关键词 {keyword.keyword} 异常: {str(e)}"
                errors.append(error_msg)
                self.logger.log_error(error_msg)

            # 避免API限流，关键词搜索API调用较重
            await asyncio.sleep(2.0)

        return {"success_count": success_count, "failed_count": failed_count, "errors": errors}

    async def _monitor_keyword_videos(self, keyword: TrendInsightKeyword) -> bool:
        """
        监控单个关键词的相关新视频

        Args:
            keyword: 关键词对象

        Returns:
            bool: 是否成功监控
        """
        try:
            # 使用抖音搜索API查找相关视频
            with profile_operation(self.monitor, f"search_keyword_videos_{keyword.keyword}"):
                videos = await self._search_keyword_videos(keyword.keyword)
                self.api_calls += 1

            if not videos:
                self.logger.log_debug(f"关键词 {keyword.keyword} 未搜索到相关视频")
                # 即使没有新视频，也更新关键词时间戳
                await self.keyword_service.update_keyword_timestamp(str(keyword.id))
                return True

            # 使用服务层处理视频数据同步
            success = await self._process_keyword_videos_with_services(keyword, videos)

            if success:
                self.logger.log_debug(f"成功监控关键词 {keyword.keyword}")
                return True
            else:
                self.logger.log_error(f"处理关键词 {keyword.keyword} 视频失败")
                return False

        except Exception as e:
            self.logger.log_error(f"监控关键词视频失败: {keyword.keyword}, 错误: {str(e)}")
            return False

    async def _search_keyword_videos(self, keyword: str) -> List:
        """
        搜索关键词相关的视频

        Args:
            keyword: 关键词

        Returns:
            List: 视频列表
        """
        try:
            # 使用抖音搜索API查找相关视频
            search_result = await self.douyin_controller.search_videos(
                keyword=keyword,
                count=self.config.keyword_video_limit,
                offset=0
            )

            if search_result and search_result.get("data"):
                # 从搜索结果中提取视频数据
                video_list = []
                for item in search_result["data"]:
                    if item.get("type") == 1 and "aweme_info" in item:
                        # 提取视频信息
                        aweme_info = item["aweme_info"]
                        video_data = {
                            "aweme_id": aweme_info.get("aweme_id"),
                            "desc": aweme_info.get("desc", ""),
                            "create_time": aweme_info.get("create_time"),
                            "author": aweme_info.get("author", {}),
                            "statistics": aweme_info.get("statistics", {}),
                            "video": aweme_info.get("video", {}),
                            "music": aweme_info.get("music", {}),
                        }
                        video_list.append(video_data)

                self.logger.log_debug(f"关键词 {keyword} 搜索到 {len(video_list)} 个视频")
                return video_list
            else:
                self.logger.log_debug(f"关键词 {keyword} 未搜索到相关视频")
                return []

        except Exception as e:
            self.logger.log_error(f"搜索关键词视频异常: {keyword}, 错误: {str(e)}")
            return []

    async def _process_keyword_videos_with_services(self, keyword: TrendInsightKeyword, videos: List) -> bool:
        """
        使用服务层处理关键词视频数据

        Args:
            keyword: 关键词对象
            videos: 视频列表

        Returns:
            bool: 是否处理成功
        """
        try:
            # 1. 同步视频数据
            video_sync_result = await self.video_sync_service.sync_videos_from_search_results(
                videos=videos,
                source_keyword=keyword.keyword,
                filter_criteria=None  # 可以根据配置添加过滤条件
            )

            if video_sync_result.errors:
                for error in video_sync_result.errors:
                    self.logger.log_error(f"视频同步错误: {error}")

            # 2. 创建关键词-视频关联关系
            if video_sync_result.video_ids:
                relation_result = await self.video_relation_service.create_keyword_video_relations(
                    keyword_id=str(keyword.id),
                    video_ids=video_sync_result.video_ids
                )

                if relation_result.errors:
                    for error in relation_result.errors:
                        self.logger.log_error(f"关联创建错误: {error}")

                # 3. 更新关键词信息
                await self.keyword_service.update_keyword_video_count(
                    str(keyword.id), len(video_sync_result.video_ids)
                )

                # 4. 同步用户订阅关联 (新功能)
                if video_sync_result.video_ids:
                    inbox_result = await self.user_inbox_service.sync_user_keyword_subscriptions(
                        keyword_id=str(keyword.id),
                        video_ids=video_sync_result.video_ids
                    )

                    if inbox_result.errors:
                        for error in inbox_result.errors:
                            self.logger.log_error(f"用户订阅同步错误: {error}")

                    if inbox_result.updated_users_count > 0:
                        self.logger.log_info(
                            f"关键词 {keyword.keyword} 订阅同步: "
                            f"更新 {inbox_result.updated_users_count} 个用户, "
                            f"创建 {inbox_result.new_relations_count} 个关联"
                        )

            # 5. 更新关键词时间戳
            await self.keyword_service.update_keyword_timestamp(str(keyword.id))

            self.logger.log_info(
                f"关键词 {keyword.keyword} 处理完成: "
                f"同步视频 {video_sync_result.new_videos_count} 个, "
                f"创建关联 {relation_result.created_count if 'relation_result' in locals() else 0} 个"
            )

            return True

        except Exception as e:
            self.logger.log_error(f"使用服务层处理关键词视频失败: {str(e)}")
            return False


