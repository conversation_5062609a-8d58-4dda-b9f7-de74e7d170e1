"""
作者监控任务 - 监控TrendInsight作者数据并同步新视频
"""

import asyncio
from datetime import datetime, timedelta
from typing import AsyncIterator, Dict, List, Union

from controllers.douyin import DouyinController
from models.trendinsight import (
    TrendInsightAuthor,
    TrendInsightVideo,
    TrendInsightVideoRelated,
)
from tasks.core.base import BaseTask
from tasks.core.logger import TaskLogger
from tasks.core.models import TaskConfig, TaskResult
from tasks.monitors.base import create_monitor, profile_operation


class AuthorMonitorTask(BaseTask):
    """作者监控任务 - 监控作者数据并同步新视频"""

    def __init__(self, config: TaskConfig, logger: TaskLogger):
        """
        初始化作者监控任务

        Args:
            config: 任务配置
            logger: 日志记录器
        """
        super().__init__(config, logger)
        self.douyin_controller = DouyinController()
        self.monitor = create_monitor("author_monitor")
        self.api_calls = 0

    async def validate_params(self) -> bool:
        """
        验证任务参数

        Returns:
            bool: 参数是否有效
        """
        try:
            # 验证过滤条件
            filters = self.config.filters or {}
            if "author_ids" in filters:
                author_ids = filters["author_ids"]
                if not isinstance(author_ids, list) or not all(isinstance(aid, str) for aid in author_ids):
                    self.logger.log_error("author_ids 过滤条件必须是字符串列表")
                    return False

            if "min_fans_count" in filters:
                min_fans_count = filters["min_fans_count"]
                if not isinstance(min_fans_count, int) or min_fans_count < 0:
                    self.logger.log_error("min_fans_count 必须是非负整数")
                    return False

            return True

        except Exception as e:
            self.logger.log_error(f"参数验证失败: {str(e)}")
            return False

    async def execute(self) -> TaskResult:
        """
        执行作者监控任务

        Returns:
            TaskResult: 执行结果
        """
        start_time = datetime.now()
        processed_count = 0
        success_count = 0
        failed_count = 0
        errors = []

        try:
            self.logger.log_progress(0, message="开始查询过期作者数据")

            # 流式查询过期作者
            async for batch_authors in self._query_stale_authors_batch():
                if self.is_interrupted:
                    self.logger.log_warning("任务被中断，停止处理")
                    break

                # 批量处理作者
                batch_results = await self._process_author_batch(batch_authors)

                # 更新统计信息
                processed_count += len(batch_authors)
                success_count += batch_results["success_count"]
                failed_count += batch_results["failed_count"]
                errors.extend(batch_results["errors"])

                # 记录进度（每50条记录）
                if processed_count % 50 == 0 or processed_count < 50:
                    self.logger.log_progress(
                        processed_count,
                        message=f"已处理 {processed_count} 个作者，成功 {success_count}，失败 {failed_count}",
                    )

                    # 记录性能指标
                    self.monitor.log_performance_metrics(processed_count=processed_count, api_calls=self.api_calls)

                    # 检查内存使用
                    self.monitor.check_memory_threshold(threshold_mb=512.0)

            # 确定任务状态
            if processed_count == 0:
                status = "success"
                self.logger.log_progress(0, message="没有需要监控的过期作者")
            elif failed_count == 0:
                status = "success"
            elif success_count > 0:
                status = "partial"
            else:
                status = "failed"

            # 生成性能报告
            performance_report = self.monitor.generate_performance_report(
                final_processed_count=processed_count, final_api_calls=self.api_calls
            )

            result = self._create_result(
                status=status,
                start_time=start_time,
                processed_count=processed_count,
                success_count=success_count,
                failed_count=failed_count,
                errors=errors,
            )

            # 添加性能报告到结果
            if hasattr(result, "performance"):
                result.performance = performance_report

            return result

        except Exception as e:
            error_msg = f"任务执行失败: {str(e)}"
            self.logger.log_error(error_msg)
            errors.append(error_msg)

            return self._create_result(
                status="failed",
                start_time=start_time,
                processed_count=processed_count,
                success_count=success_count,
                failed_count=failed_count,
                errors=errors,
            )

    async def _query_stale_authors_batch(self) -> AsyncIterator[List[TrendInsightAuthor]]:
        """
        流式批量查询过期作者

        Yields:
            List[TrendInsightAuthor]: 作者批次
        """
        try:
            # 计算过期时间阈值
            cutoff_time = datetime.now() - timedelta(hours=self.config.max_age_hours)

            # 构建查询条件
            query = TrendInsightAuthor.filter(updated_at__lte=cutoff_time)

            # 应用过滤条件
            filters = self.config.filters or {}
            if "author_ids" in filters:
                author_ids = filters["author_ids"]
                query = query.filter(id__in=author_ids)

            if "min_fans_count" in filters:
                min_fans_count = filters["min_fans_count"]
                query = query.filter(fans_count_int__gte=min_fans_count)

            # 只处理有 douyin_user_id 的作者
            query = query.filter(douyin_user_id__isnull=False, douyin_user_id__not="")

            # 分批查询
            offset = 0
            batch_size = self.config.batch_size

            while True:
                if self.is_interrupted:
                    break

                batch = await query.offset(offset).limit(batch_size).all()
                if not batch:
                    break

                yield batch
                offset += batch_size

                # 批次间短暂休息，避免数据库压力
                await asyncio.sleep(0.1)

        except Exception as e:
            self.logger.log_error(f"查询过期作者失败: {str(e)}")
            raise

    async def _process_author_batch(self, authors: List[TrendInsightAuthor]) -> Dict[str, Union[int, List[str]]]:
        """
        批量处理作者监控

        Args:
            authors: 作者列表

        Returns:
            dict: 批次处理结果
        """
        success_count = 0
        failed_count = 0
        errors = []

        for author in authors:
            if self.is_interrupted:
                break

            try:
                success = await self._monitor_author_videos(author)
                if success:
                    success_count += 1
                else:
                    failed_count += 1
                    errors.append(f"监控作者 {author.user_id} 视频失败")

            except Exception as e:
                failed_count += 1
                error_msg = f"处理作者 {author.user_id} 异常: {str(e)}"
                errors.append(error_msg)
                self.logger.log_error(error_msg)

            # 避免API限流
            await asyncio.sleep(1.0)

        return {"success_count": success_count, "failed_count": failed_count, "errors": errors}

    async def _monitor_author_videos(self, author: TrendInsightAuthor) -> bool:
        """
        监控单个作者的新视频

        Args:
            author: 作者对象

        Returns:
            bool: 是否成功监控
        """
        try:
            if not author.douyin_user_id:
                self.logger.log_warning(f"作者 {author.user_id} 没有有效的 douyin_user_id")
                return False

            # 调用抖音API获取作者的视频列表
            try:
                with profile_operation(self.monitor, f"get_author_videos_{author.user_id}"):
                    # 注意：这个功能需要有效的cookies，暂时跳过
                    # TODO: 需要重新设计这个功能，因为获取用户收藏夹需要登录状态
                    self.logger.log_warning(f"作者 {author.user_id} 的视频监控功能暂时禁用，需要有效的cookies")
                    result = None
                    self.api_calls += 1

                if not result or not hasattr(result, "aweme_list") or not result.aweme_list:
                    self.logger.log_debug(f"作者 {author.user_id} 未获取到视频数据")
                    # 即使没有新视频，也更新作者的 updated_at
                    author.updated_at = datetime.now()
                    await author.save()
                    return True

                # 处理获取到的视频数据
                new_videos_count = await self._sync_videos_and_relations(result.aweme_list, str(author.id), "author")

                # 更新作者的 updated_at 时间戳
                author.updated_at = datetime.now()
                await author.save()

                self.logger.log_debug(f"成功监控作者 {author.user_id}，同步了 {new_videos_count} 个新视频")
                return True

            except Exception as e:
                self.logger.log_error(f"调用抖音API失败: {author.user_id}, 错误: {str(e)}")
                return False

        except Exception as e:
            self.logger.log_error(f"监控作者视频失败: {author.user_id}, 错误: {str(e)}")
            return False

    async def _sync_videos_and_relations(self, videos: List, source_id: str, source_type: str) -> int:
        """
        同步视频数据并创建关联关系

        Args:
            videos: 视频列表
            source_id: 来源ID（作者ID）
            source_type: 来源类型（"author"）

        Returns:
            int: 同步的新视频数量
        """
        new_videos_count = 0

        for video_data in videos:
            try:
                # 获取视频ID
                aweme_id = getattr(video_data, "aweme_id", None) or getattr(video_data, "id", None)
                if not aweme_id:
                    continue

                # 检查视频是否已存在
                existing_video = await TrendInsightVideo.filter(id=aweme_id).first()

                if not existing_video:
                    # 创建新的视频记录
                    video_record = TrendInsightVideo(
                        id=aweme_id,
                        trend_score=0.0,
                        trend_radio=0.0,
                        created_at=datetime.now(),
                        updated_at=datetime.now(),
                        is_deleted=False,
                    )
                    await video_record.save()
                    new_videos_count += 1

                # 检查关联关系是否已存在
                existing_relation = await TrendInsightVideoRelated.filter(
                    source_id=source_id, video_id=aweme_id
                ).first()

                if not existing_relation:
                    # 创建视频关联关系
                    relation = TrendInsightVideoRelated(
                        source_id=source_id,
                        source_type=source_type,
                        video_id=aweme_id,
                        created_at=datetime.now(),
                        updated_at=datetime.now(),
                        is_deleted=False,
                    )
                    await relation.save()

            except Exception as e:
                self.logger.log_error(f"同步视频 {aweme_id} 失败: {str(e)}")
                continue

        return new_videos_count
