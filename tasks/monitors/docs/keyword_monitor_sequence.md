# 关键词监控任务 - 时序图

本文档使用 Mermaid 语法描述 `KeywordMonitorTask` 与其他组件（主要是服务层）的交互时序。

## 1. 整体执行时序

```mermaid
sequenceDiagram
    participant Task as KeywordMonitorTask
    participant DB as 数据库
    participant Services as 服务层

    Task->>DB: _query_stale_keywords_batch()
    DB-->>Task: 返回一批过期的关键词

    loop 对每个关键词
        Task->>Services: _monitor_keyword_videos(keyword)
        Services-->>Task: 返回监控结果 (True/False)
    end

    Task->>Task: 汇总结果
    Task->>Task: 生成性能报告
    Task-->>Caller: 返回 TaskResult
```

## 2. 单个关键词监控详细时序 (`_monitor_keyword_videos`)

这个时序图展示了任务如何与外部API和服务层进行交互来处理一个关键词。

```mermaid
sequenceDiagram
    participant Task as KeywordMonitorTask
    participant DouyinAPI as 抖音搜索API (模拟)
    participant Services as 服务层

    Task->>DouyinAPI: _search_keyword_videos(keyword)
    Note right of Task: 模拟调用, 当前返回空
    DouyinAPI-->>Task: 返回视频列表

    alt 未找到视频
        Task->>Services: KeywordService.update_keyword_timestamp()
        Services-->>Task: 更新成功
    else 找到视频
        Task->>Services: _process_keyword_videos_with_services(keyword, videos)
        Services-->>Task: 返回处理结果 (True/False)
    end
```

## 3. 服务层协作时序 (`_process_keyword_videos_with_services`)

这个时序图详细展示了 `_process_keyword_videos_with_services` 方法内部各个服务之间的调用顺序和协作关系。

```mermaid
sequenceDiagram
    participant Task as KeywordMonitorTask
    participant VSS as VideoSyncService
    participant VRS as VideoRelationService
    participant KS as KeywordService
    participant UIS as UserInboxService
    participant DB as 数据库

    Task->>VSS: sync_videos_from_search_results(videos, keyword)
    VSS->>DB: 批量查询已存在的视频
    DB-->>VSS: 返回已存在的视频ID
    VSS->>DB: 批量创建新视频
    DB-->>VSS: 创建成功
    VSS-->>Task: 返回同步结果 (video_sync_result)

    alt 视频同步成功且有新视频
        Task->>VRS: create_keyword_video_relations(keyword_id, video_ids)
        VRS->>DB: 批量查询已存在的关联
        DB-->>VRS: 返回已存在的关联
        VRS->>DB: 批量创建新关联
        DB-->>VRS: 创建成功
        VRS-->>Task: 返回关联创建结果

        Task->>KS: update_keyword_video_count(keyword_id, count)
        KS->>DB: 更新关键词视频数量
        DB-->>KS: 更新成功
        KS-->>Task: 

        Task->>UIS: sync_user_keyword_subscriptions(keyword_id, video_ids)
        UIS->>DB: 查询订阅了该关键词的用户
        DB-->>UIS: 返回用户列表
        UIS->>DB: 批量创建用户收件箱记录
        DB-->>UIS: 创建成功
        UIS-->>Task: 返回订阅同步结果
    end

    Task->>KS: update_keyword_timestamp(keyword_id)
    KS->>DB: 更新关键词的 updated_at 字段
    DB-->>KS: 更新成功
    KS-->>Task: 
```
