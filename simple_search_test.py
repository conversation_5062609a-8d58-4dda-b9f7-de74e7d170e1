#!/usr/bin/env python3
"""
简单的关键词搜索功能测试

只测试RPC API层面的功能，不涉及数据库
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from rpc.douyin.api import AsyncDouyinAPI
from rpc.douyin.schemas import SearchInfoRequest


async def test_rpc_search():
    """测试RPC搜索功能"""
    print("=== 测试抖音RPC搜索功能 ===")
    
    try:
        # 创建API客户端
        api = AsyncDouyinAPI()
        
        # 创建搜索请求
        request = SearchInfoRequest(
            keyword="美食",
            count=5,
            offset=0
        )
        
        print(f"搜索关键词: {request.keyword}")
        print(f"请求参数: count={request.count}, offset={request.offset}")
        
        # 执行搜索
        response = await api.search_info_by_keyword(request)
        
        print(f"✅ 搜索成功!")
        print(f"状态码: {response.status_code}")
        print(f"数据数量: {len(response.data)}")
        print(f"是否有更多: {response.has_more}")
        print(f"游标: {response.cursor}")
        
        # 显示前几个结果
        for i, item in enumerate(response.data[:3], 1):
            print(f"  {i}. 类型: {item.get('type', 'unknown')}")
            if item.get('type') == 1 and 'aweme_info' in item:
                aweme_info = item['aweme_info']
                print(f"     描述: {aweme_info.get('desc', '')[:50]}...")
                print(f"     作者: {aweme_info.get('author', {}).get('nickname', '未知')}")
                print(f"     视频ID: {aweme_info.get('aweme_id')}")
        
        return True
        
    except Exception as e:
        print(f"❌ RPC搜索测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_search_videos_method():
    """测试search_videos方法"""
    print("\n=== 测试search_videos方法 ===")
    
    try:
        # 创建API客户端
        api = AsyncDouyinAPI()
        
        # 创建搜索请求
        request = SearchInfoRequest(
            keyword="旅游",
            count=3,
            offset=0
        )
        
        print(f"搜索关键词: {request.keyword}")
        
        # 执行搜索（使用原有的search_videos方法）
        response = await api.search_videos(request)
        
        print(f"✅ search_videos方法测试成功!")
        print(f"状态码: {response.status_code}")
        print(f"数据数量: {len(response.data)}")
        
        return True
        
    except Exception as e:
        print(f"❌ search_videos方法测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("开始测试关键词搜索功能...\n")
    
    # 测试RPC搜索功能
    rpc_success = await test_rpc_search()
    
    # 测试search_videos方法
    search_success = await test_search_videos_method()
    
    # 总结测试结果
    print("\n=== 测试结果总结 ===")
    print(f"RPC搜索功能: {'✅ 成功' if rpc_success else '❌ 失败'}")
    print(f"search_videos方法: {'✅ 成功' if search_success else '❌ 失败'}")
    
    if rpc_success and search_success:
        print("\n🎉 RPC层测试通过！")
        return 0
    else:
        print("\n⚠️  部分测试失败，请检查实现。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
