#!/usr/bin/env python3
"""
API端点测试脚本

测试新实现的搜索视频API端点
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import httpx


async def test_search_videos_api():
    """测试搜索视频API端点"""
    print("=== 测试搜索视频API端点 ===")
    
    # API基础URL（假设服务运行在8000端口）
    base_url = "http://localhost:8000"
    endpoint = "/api/v1/douyin/search/videos"
    
    # 测试参数
    params = {
        "keyword": "美食",
        "count": 5,
        "offset": 0
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print(f"请求URL: {base_url}{endpoint}")
            print(f"请求参数: {params}")
            
            response = await client.get(f"{base_url}{endpoint}", params=params)
            
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API调用成功!")
                print(f"状态码: {data.get('status_code')}")
                print(f"数据数量: {len(data.get('data', []))}")
                print(f"是否有更多: {data.get('has_more')}")
                
                # 显示前几个结果
                for i, item in enumerate(data.get('data', [])[:3], 1):
                    if item.get('type') == 1 and 'aweme_info' in item:
                        aweme_info = item['aweme_info']
                        print(f"  {i}. {aweme_info.get('desc', '')[:50]}...")
                        print(f"     作者: {aweme_info.get('author', {}).get('nickname', '未知')}")
                        print(f"     视频ID: {aweme_info.get('aweme_id')}")
                
                return True
            else:
                print(f"❌ API调用失败: HTTP {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
                
    except httpx.ConnectError:
        print("❌ 无法连接到服务器，请确保服务正在运行")
        print("提示：运行 'python main.py' 或 'uvicorn main:app --reload' 启动服务")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False


async def test_discover_search_api():
    """测试发现搜索API端点（对比测试）"""
    print("\n=== 测试发现搜索API端点（对比） ===")
    
    # API基础URL
    base_url = "http://localhost:8000"
    endpoint = "/api/v1/douyin/discover/search"
    
    # 测试参数
    params = {
        "keyword": "美食",
        "count": 5,
        "offset": 0
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print(f"请求URL: {base_url}{endpoint}")
            print(f"请求参数: {params}")
            
            response = await client.get(f"{base_url}{endpoint}", params=params)
            
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 发现搜索API调用成功!")
                print(f"状态码: {data.get('status_code')}")
                print(f"用户列表数量: {len(data.get('user_list', []))}")
                print(f"是否有更多: {data.get('has_more')}")
                
                return True
            else:
                print(f"❌ 发现搜索API调用失败: HTTP {response.status_code}")
                return False
                
    except httpx.ConnectError:
        print("❌ 无法连接到服务器")
        return False
    except Exception as e:
        print(f"❌ 发现搜索API测试失败: {str(e)}")
        return False


def print_usage_examples():
    """打印使用示例"""
    print("\n=== API使用示例 ===")
    print("1. 基础搜索:")
    print('   curl "http://localhost:8000/api/v1/douyin/search/videos?keyword=美食"')
    print()
    print("2. 分页搜索:")
    print('   curl "http://localhost:8000/api/v1/douyin/search/videos?keyword=美食&offset=20&count=10"')
    print()
    print("3. 带搜索ID:")
    print('   curl "http://localhost:8000/api/v1/douyin/search/videos?keyword=美食&search_id=123456"')
    print()
    print("4. 使用Python requests:")
    print("""
import requests

response = requests.get(
    "http://localhost:8000/api/v1/douyin/search/videos",
    params={"keyword": "美食", "count": 10}
)
data = response.json()
print(f"找到 {len(data['data'])} 个视频")
""")


async def main():
    """主测试函数"""
    print("开始测试搜索视频API端点...\n")
    
    # 测试搜索视频API
    search_success = await test_search_videos_api()
    
    # 测试发现搜索API（对比）
    discover_success = await test_discover_search_api()
    
    # 总结测试结果
    print("\n=== 测试结果总结 ===")
    print(f"搜索视频API: {'✅ 成功' if search_success else '❌ 失败'}")
    print(f"发现搜索API: {'✅ 成功' if discover_success else '❌ 失败'}")
    
    if search_success:
        print("\n🎉 搜索视频API端点测试通过！")
        print_usage_examples()
        return 0
    else:
        print("\n⚠️  API端点测试失败，请检查服务是否正在运行。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
